import { Box, Typography } from '@mui/material';
import { useQuery } from 'react-query';
import { getMarfookInstructions } from 'apis/marfook';
import { PATHS } from '../../../constants';
import LoadingPage from '../../../components/LoadingPage/LoadingPage';
import InstructionSection from './components/MembersSection/InstructionSection';

export default function MarfookInstructions() {
  const { isLoading, data } = useQuery([], () => getMarfookInstructions());
  const instructionList = isLoading ? {} : data?.data?.results || [];

  if (isLoading) {
    return <LoadingPage />;
  }

  return (
    <>
      <Box sx={{ width: '100%', marginBottom: '20px', textAlign: 'center' }}>
        <Typography
          variant="h4"
          sx={{ mt: 3, fontSize: 17, fontWeight: 'bold' }}
        >
          تابلو اعلانات مرفوک
        </Typography>
      </Box>

      <InstructionSection list={instructionList} />
    </>
  );
}
