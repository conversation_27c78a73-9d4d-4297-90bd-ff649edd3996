{"name": "tam-front", "version": "0.1.0", "private": true, "dependencies": {"@emotion/cache": "^11.10.7", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@jitsi/react-sdk": "^1.4.0", "@mui/icons-material": "^5.11.0", "@mui/lab": "^6.0.0-beta.14", "@mui/material": "^5.11.8", "@mui/system": "^5.16.4", "@mui/x-date-pickers": "^6.1.0", "@mui/x-tree-view": "^7.22.1", "@reduxjs/toolkit": "^1.9.2", "@sentry/react": "^7.64.0", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.5.0", "axios": "1.7.4", "chart.js": "^4.3.0", "clsx": "^2.1.1", "d3": "^7.9.0", "d3-org-chart": "^3.1.1", "dayjs": "^1.11.7", "emoji-picker-react": "4.5.6", "eslint": "^7.32.0 || ^8.2.0", "firebase": "10.9.0", "highcharts": "^11.4.1", "highcharts-react-official": "^3.2.1", "html2canvas": "^1.4.1", "html2canvas-pro": "^1.5.8", "jalaliday": "^2.3.0", "jspdf": "^2.5.2", "lodash.debounce": "^4.0.8", "opentype.js": "^1.2.5", "react": "^18.2.0", "react-cookie": "^4.1.1", "react-dom": "^18.2.0", "react-easy-crop": "^4.7.3", "react-image-crop": "^11.0.1", "react-intersection-observer": "^9.4.3", "react-mobile-cropper": "^0.10.0", "react-organizational-chart": "^2.2.1", "react-player": "^2.12.0", "react-query": "^3.39.3", "react-redux": "^8.0.5", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "react-slick": "^0.29.0", "react-swipeable": "^7.0.1", "react-use-downloader": "^1.2.4", "react-use-websocket": "^4.3.1", "react-zoom-pan-pinch": "^2.6.1", "recharts": "^2.5.0", "slick-carousel": "^1.8.1", "stylis": "^4.1.3", "stylis-plugin-rtl": "^2.1.1", "use-long-press": "^3.2.0", "uuid": "^9.0.1", "web-vitals": "^2.1.4", "workbox-background-sync": "^6.4.2", "workbox-broadcast-update": "^6.4.2", "workbox-cacheable-response": "^6.4.2", "workbox-core": "^6.4.2", "workbox-expiration": "^6.4.2", "workbox-google-analytics": "^6.4.2", "workbox-navigation-preload": "^6.4.2", "workbox-precaching": "^6.4.2", "workbox-range-requests": "^6.4.2", "workbox-routing": "^6.4.2", "workbox-strategies": "^6.4.2", "workbox-streams": "^6.4.2"}, "scripts": {"start": "react-scripts start", "build": "set REACT_APP_ENV=production && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --color --fix --ext .jsx,.js", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org sentry --project tam-frontend ./build && sentry-cli --url https://sentry.haghighatgram.ir/ sourcemaps upload --org sentry --project tam-frontend ./build"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0"}}