import {
  Box,
  Button,
  Chip,
  CircularProgress,
  Divider,
  Grid,
  IconButton,
  TextField,
  Typography,
} from '@mui/material';
import { useMutation, useQuery } from 'react-query';
import { useRef, useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import {
  ContentPasteGoOutlined,
  Add,
  Delete,
  ArrowBack,
  Save,
} from '@mui/icons-material';
import {
  getMarfookInstruction,
  updateMarfookInstruction,
} from '../../../apis/marfook';
import { setSnackbar } from '../../../store/layout';
import { PATHS } from '../../../constants';
import LoadingPage from '../../../components/LoadingPage/LoadingPage';

export default function MarfookInstructionEdit() {
  const { instructionId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();

  const descriptionRef = useRef(null);
  const formRef = useRef(null);

  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(false);
  const [instructions, setInstructions] = useState([
    { title: '', hashtags: [] },
  ]);
  const [title, setTitle] = useState('');
  const [body, setBody] = useState('');

  // Fetch instruction data
  const { isLoading, data, error } = useQuery(
    [PATHS.admin.marfookInstructions, instructionId],
    () => getMarfookInstruction(instructionId),
    {
      enabled: !!instructionId,
      onSuccess: data => {
        const instruction = data?.data;
        if (instruction) {
          setTitle(instruction.title || '');
          setBody(instruction.body || '');
          setInstructions(
            instruction.instructions || [{ title: '', hashtags: [] }],
          );
        }
      },
    },
  );

  // Use location state if available (from navigation)
  useEffect(() => {
    if (location.state?.instruction) {
      const instruction = location.state.instruction;
      setTitle(instruction.title || '');
      setBody(instruction.body || '');
      setInstructions(
        instruction.instructions || [{ title: '', hashtags: [] }],
      );
    }
  }, [location.state]);

  const pasteDescription = async () => {
    const text = await navigator.clipboard.readText();
    setBody(text);
    if (descriptionRef.current) {
      descriptionRef.current.value = text;
    }
  };

  // Functions for managing instructions
  const addInstruction = () => {
    setInstructions([...instructions, { title: '', hashtags: [] }]);
  };

  const removeInstruction = index => {
    if (instructions.length > 1) {
      setInstructions(instructions.filter((_, i) => i !== index));
    }
  };

  const updateInstructionTitle = (index, title) => {
    const newInstructions = [...instructions];
    newInstructions[index].title = title;
    setInstructions(newInstructions);
  };

  const updateInstructionHashtags = (index, hashtags) => {
    const newInstructions = [...instructions];
    newInstructions[index].hashtags = hashtags;
    setInstructions(newInstructions);
  };

  const handleHashtagInput = (index, value) => {
    const hashtagsArray = value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
      .map(tag => (tag.startsWith('#') ? tag : `#${tag}`));

    updateInstructionHashtags(index, hashtagsArray);
  };

  const handleHashtagPaste = (index, event) => {
    event.preventDefault();
    const pastedText = event.clipboardData.getData('text');

    if (!pastedText.trim()) return;

    // Split by spaces, newlines, and commas to handle different formats
    const hashtagsArray = pastedText
      .split(/[\s,\n\r]+/)
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
      .map(tag => (tag.startsWith('#') ? tag : `#${tag}`));

    if (hashtagsArray.length === 0) return;

    // Get existing hashtags and combine with new ones
    const existingHashtags = instructions[index]?.hashtags || [];
    const combinedHashtags = [...existingHashtags];

    hashtagsArray.forEach(hashtag => {
      if (!combinedHashtags.includes(hashtag)) {
        combinedHashtags.push(hashtag);
      }
    });

    updateInstructionHashtags(index, combinedHashtags);

    // Clear the input field
    event.target.value = '';
  };

  const removeHashtag = (instructionIndex, hashtagIndex) => {
    const newInstructions = [...instructions];
    newInstructions[instructionIndex].hashtags.splice(hashtagIndex, 1);
    setInstructions(newInstructions);
  };

  const abortController = useRef(null);
  const mutation = useMutation(
    ({ event }) => {
      setLoading(true);

      if (title === '') {
        throw { message: 'عنوان اجباری است', severity: 'error' };
      } else if (body === '') {
        throw { message: 'توضیحات اعلان اجباری است', severity: 'error' };
      } else if (instructions.some(inst => inst.title === '')) {
        throw {
          message: 'عنوان همه دستورالعمل‌ها اجباری است',
          severity: 'error',
        };
      }

      return updateMarfookInstruction(
        instructionId,
        {
          title: title,
          body: body,
          instructions: instructions,
        },
        setProgress,
        abortController.current,
      );
    },
    {
      onSuccess: async () => {
        dispatch(
          setSnackbar({
            message: 'اعلان مرفوک با موفقیت به‌روزرسانی شد',
            severity: 'success',
          }),
        );
        setProgress(0);
        setLoading(false);
        navigate(`${PATHS.admin.marfookInstructions}/${instructionId}`);
      },
      onError: error => {
        if (error?.response?.status === 400) {
          dispatch(
            setSnackbar({
              message: 'خطا در به‌روزرسانی اعلان مرفوک',
              severity: 'error',
            }),
          );
        } else dispatch(setSnackbar(error));

        setProgress(0);
        setLoading(false);
      },
    },
  );

  const errors = mutation.error?.response?.data || {};

  const submitForm = event => {
    abortController.current = new AbortController();
    event.preventDefault();
    mutation.mutate({ event });
  };

  const handleBack = () => {
    navigate(`${PATHS.admin.marfookInstructions}/${instructionId}`);
  };

  if (isLoading) {
    return <LoadingPage />;
  }

  if (error) {
    return (
      <Box sx={{ textAlign: 'center', mt: 4 }}>
        <Typography variant="h6" color="error">
          خطا در بارگذاری اعلان
        </Typography>
        <Button
          variant="contained"
          onClick={() => navigate(PATHS.admin.marfookInstructions)}
          sx={{ mt: 2 }}
        >
          بازگشت به لیست
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', height: '100%', overflowY: 'scroll', p: 2 }}>
      {/* Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: 3,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBack />
          </IconButton>
          <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
            ویرایش اعلان مرفوک
          </Typography>
        </Box>
      </Box>

      <form onSubmit={submitForm} ref={formRef}>
        <Grid container columnSpacing={2}>
          <Grid item xs={12} lg={8}>
            <TextField
              variant="outlined"
              required
              label="عنوان"
              value={title}
              onChange={e => setTitle(e.target.value)}
              helperText={errors.title}
              error={!!errors.title}
              disabled={loading}
              inputProps={{ maxLength: 100 }}
              fullWidth
            />

            <TextField
              sx={{ mt: 2 }}
              label="توضیحات اعلان"
              variant="outlined"
              helperText={errors.body}
              multiline
              rows={4}
              fullWidth
              value={body}
              onChange={e => setBody(e.target.value)}
              inputRef={descriptionRef}
              InputProps={{
                endAdornment: (
                  <IconButton edge="end" onClick={pasteDescription}>
                    <ContentPasteGoOutlined />
                  </IconButton>
                ),
              }}
            />

            <Divider sx={{ mt: 3, mb: 3 }} />

            <Typography sx={{ fontSize: 16, fontWeight: 'bold' }}>
              دستورالعمل‌ها
            </Typography>

            {instructions.map((instruction, index) => (
              <Box
                key={index}
                sx={{
                  mt: 2,
                  p: 2,
                  border: '1px solid #e0e0e0',
                  borderRadius: 2,
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 2,
                  }}
                >
                  <Typography sx={{ fontSize: 14, fontWeight: 'bold' }}>
                    دستورالعمل {index + 1}
                  </Typography>
                  {instructions.length > 1 && (
                    <IconButton
                      onClick={() => removeInstruction(index)}
                      size="small"
                      color="error"
                    >
                      <Delete />
                    </IconButton>
                  )}
                </Box>

                <TextField
                  fullWidth
                  label="عنوان دستورالعمل"
                  value={instruction.title}
                  onChange={e => updateInstructionTitle(index, e.target.value)}
                  sx={{ mb: 2 }}
                  required
                />

                <TextField
                  fullWidth
                  label="هشتگ‌ها (با کاما جدا کنید)"
                  placeholder="مثال: تگ1, تگ2, تگ3 یا paste کنید"
                  defaultValue={instruction.hashtags?.join(', ')}
                  onBlur={e => handleHashtagInput(index, e.target.value)}
                  onPaste={e => handleHashtagPaste(index, e)}
                  sx={{ mb: 2 }}
                />

                {instruction.hashtags && instruction.hashtags.length > 0 && (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {instruction.hashtags.map((hashtag, hashtagIndex) => (
                      <Chip
                        key={hashtagIndex}
                        label={hashtag}
                        onDelete={() => removeHashtag(index, hashtagIndex)}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                )}
              </Box>
            ))}

            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={addInstruction}
              sx={{ mt: 2, width: '100%' }}
            >
              افزودن دستورالعمل جدید
            </Button>

            <Button
              variant="contained"
              size="large"
              type="submit"
              startIcon={<Save />}
              sx={{ mt: 4, width: '100%' }}
              disabled={loading}
            >
              {!loading ? (
                <>ذخیره تغییرات</>
              ) : (
                <CircularProgress sx={{ color: 'white' }} size={24} />
              )}
            </Button>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
}
