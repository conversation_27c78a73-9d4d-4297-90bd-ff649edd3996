import {
  Box,
  Button,
  Chip,
  CircularProgress,
  Divider,
  Grid,
  IconButton,
  TextField,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  Switch,
  Card,
  CardContent,
  Stack,
} from '@mui/material';
import { useMutation } from 'react-query';
import { useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { ContentPasteGoOutlined, Add, Delete, Edit } from '@mui/icons-material';
import { createMarfookInstruction } from '../../../apis/marfook';
import { setSnackbar } from '../../../store/layout';
import JalaliDateTimePicker from '../../../components/JalaliDateTimePicker/JalaliDateTimePicker';
import MultiSelectAdmins from '../AnnouncementCreate/components/MultiSelectAdmins';
import MultiSelectOrganization from '../AnnouncementCreate/components/MultiSelectOrganization';
import MultiSelectMembers from '../AnnouncementCreate/components/MultiSelectMember';
import dayjs from 'dayjs';
import JalaliDatePicker from 'components/SearchBoxMarfook/components/JalaliDatePicker/JalaliDatePicker';

export default function MarfookInstructionCreate({ content = {} }) {
  const descriptionRef = useRef(null);
  const formRef = useRef(null);
  const dispatch = useDispatch();
  const abortController = useRef(null);

  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(false);

  // State for instructions (محورها)
  const [instructions, setInstructions] = useState([]);

  // State for expiration date
  const [expirationDate, setExpirationDate] = useState(null);

  // State for new fields
  const [instructionNumber, setInstructionNumber] = useState('');
  const [generalHashtags, setGeneralHashtags] = useState([]);
  const [registrationDate, setRegistrationDate] = useState(dayjs());

  // State for adding new instruction (محور جدید)
  const [newInstructionTitle, setNewInstructionTitle] = useState('');
  const [newInstructionHashtags, setNewInstructionHashtags] = useState([]);
  const [editingIndex, setEditingIndex] = useState(-1); // -1 means not editing

  // State for notification recipients (اطلاع رسانی به)
  const [sendToOrgan, setSendToOrgan] = useState(false);
  const [sendToAdmins, setSendToAdmins] = useState(false);
  const [sendToMembers, setSendToMembers] = useState(false);
  const [selectedMembers, setSelectedMembers] = useState([]);
  const [selectedAdmins, setSelectedAdmins] = useState([]);
  const [selectedOrganizations, setSelectedOrganizations] = useState([]);

  // State for notification methods (روش اطلاع رسانی)
  const [useChat, setUseChat] = useState(true);
  const [useSms, setUseSms] = useState(false);

  const pasteDescription = async () => {
    descriptionRef.current.value = await navigator.clipboard.readText();
  };

  // Functions for managing instructions (محورها)
  const addOrUpdateInstruction = () => {
    if (!newInstructionTitle.trim()) {
      dispatch(
        setSnackbar({
          message: 'عنوان محور اجباری است',
          severity: 'error',
        }),
      );
      return;
    }

    const newInstruction = {
      title: newInstructionTitle.trim(),
      hashtags: [...newInstructionHashtags],
    };

    if (editingIndex >= 0) {
      // Update existing instruction
      const updatedInstructions = [...instructions];
      updatedInstructions[editingIndex] = newInstruction;
      setInstructions(updatedInstructions);
      setEditingIndex(-1);
    } else {
      // Add new instruction
      setInstructions([...instructions, newInstruction]);
    }

    // Clear form
    setNewInstructionTitle('');
    setNewInstructionHashtags([]);
  };

  const editInstruction = index => {
    const instruction = instructions[index];
    setNewInstructionTitle(instruction.title);
    setNewInstructionHashtags([...instruction.hashtags]);
    setEditingIndex(index);
  };

  const removeInstruction = index => {
    setInstructions(instructions.filter((_, i) => i !== index));
    // If we were editing this instruction, cancel editing
    if (editingIndex === index) {
      setEditingIndex(-1);
      setNewInstructionTitle('');
      setNewInstructionHashtags([]);
    }
  };

  const cancelEdit = () => {
    setEditingIndex(-1);
    setNewInstructionTitle('');
    setNewInstructionHashtags([]);
  };

  // Handle hashtag input for new instruction
  const handleNewInstructionHashtagInput = value => {
    if (!value.trim()) return;

    const newHashtagsArray = value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
      .map(tag => (tag.startsWith('#') ? tag : `#${tag}`));

    if (newHashtagsArray.length === 0) return;

    const combinedHashtags = [...newInstructionHashtags];
    newHashtagsArray.forEach(hashtag => {
      if (!combinedHashtags.includes(hashtag)) {
        combinedHashtags.push(hashtag);
      }
    });

    setNewInstructionHashtags(combinedHashtags);
  };

  const handleNewInstructionHashtagPaste = event => {
    event.preventDefault();
    const pastedText = event.clipboardData.getData('text');

    if (!pastedText.trim()) return;

    // Split by spaces, newlines, and commas to handle different formats
    const newHashtagsArray = pastedText
      .split(/[\s,\n\r]+/)
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
      .map(tag => (tag.startsWith('#') ? tag : `#${tag}`));

    if (newHashtagsArray.length === 0) return;

    const combinedHashtags = [...newInstructionHashtags];
    newHashtagsArray.forEach(hashtag => {
      if (!combinedHashtags.includes(hashtag)) {
        combinedHashtags.push(hashtag);
      }
    });

    setNewInstructionHashtags(combinedHashtags);

    // Clear the input field
    event.target.value = '';
  };

  const handleNewInstructionHashtagKeyDown = event => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleNewInstructionHashtagInput(event.target.value);
      event.target.value = '';
    }
  };

  const removeNewInstructionHashtag = index => {
    const newHashtags = [...newInstructionHashtags];
    newHashtags.splice(index, 1);
    setNewInstructionHashtags(newHashtags);
  };

  // Functions for managing general hashtags
  const handleGeneralHashtagInput = value => {
    if (!value.trim()) return;

    const newHashtagsArray = value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
      .map(tag => (tag.startsWith('#') ? tag : `#${tag}`));

    if (newHashtagsArray.length === 0) return;

    const combinedHashtags = [...generalHashtags];
    newHashtagsArray.forEach(hashtag => {
      if (!combinedHashtags.includes(hashtag)) {
        combinedHashtags.push(hashtag);
      }
    });

    setGeneralHashtags(combinedHashtags);
  };

  const handleGeneralHashtagPaste = event => {
    event.preventDefault();
    const pastedText = event.clipboardData.getData('text');

    if (!pastedText.trim()) return;

    // Split by spaces, newlines, and commas to handle different formats
    const newHashtagsArray = pastedText
      .split(/[\s,\n\r]+/)
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
      .map(tag => (tag.startsWith('#') ? tag : `#${tag}`));

    if (newHashtagsArray.length === 0) return;

    const combinedHashtags = [...generalHashtags];
    newHashtagsArray.forEach(hashtag => {
      if (!combinedHashtags.includes(hashtag)) {
        combinedHashtags.push(hashtag);
      }
    });

    setGeneralHashtags(combinedHashtags);

    // Clear the input field
    event.target.value = '';
  };

  const handleGeneralHashtagKeyDown = event => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleGeneralHashtagInput(event.target.value);
      event.target.value = '';
    }
  };

  const removeGeneralHashtag = index => {
    const newHashtags = [...generalHashtags];
    newHashtags.splice(index, 1);
    setGeneralHashtags(newHashtags);
  };

  const mutation = useMutation(
    ({ event }) => {
      setLoading(true);

      const { title, body } = event.target;
      if (!instructionNumber) {
        throw { message: 'شماره دستور جز به جز اجباری است', severity: 'error' };
      } else if (title.value === '') {
        throw { message: 'عنوان اجباری است', severity: 'error' };
      } else if (!registrationDate) {
        throw { message: 'تاریخ ثبت اجباری است', severity: 'error' };
      } else if (!expirationDate) {
        throw { message: 'تاریخ انقضا اجباری است', severity: 'error' };
      } else if (instructions.some(inst => inst.title === '')) {
        throw {
          message: 'عنوان همه دستورالعمل‌ها اجباری است',
          severity: 'error',
        };
      }

      // Format data according to API template
      const requestData = {
        code: instructionNumber,
        hashtags: generalHashtags,
        registration_date: registrationDate,
        subject: instructions.map(inst => ({
          title: inst.title,
          hashtags: inst.hashtags,
        })),
        title: title.value,
        description: body.value,
        expiration_date: expirationDate,
      };

      // Add notification data if any notification method is enabled
      if (sendToAdmins || sendToMembers || sendToOrgan) {
        requestData.notification = {
          receiving_organizations: selectedOrganizations.map(x => x.id) || [],
          receiving_users: sendToAdmins
            ? selectedAdmins.map(x => x.id) || []
            : selectedMembers.map(x => x.id) || [],
          use_sms: useSms,
          use_chat: useChat,
        };
      }

      return createMarfookInstruction(
        requestData,
        setProgress,
        abortController.current,
      );
    },
    {
      onSuccess: async () => {
        dispatch(
          setSnackbar({
            message: 'اعلان مرفوک با موفقیت ایجاد شد',
            severity: 'success',
          }),
        );
        setProgress(0);
        setLoading(false);
        formRef.current.reset();
        setInstructions([]);
        setExpirationDate(null);
        setInstructionNumber('');
        setGeneralHashtags([]);
        setRegistrationDate(dayjs());
        setNewInstructionTitle('');
        setNewInstructionHashtags([]);
        setEditingIndex(-1);
        // Reset notification states
        setSendToOrgan(false);
        setSendToAdmins(false);
        setSendToMembers(false);
        setSelectedMembers([]);
        setSelectedAdmins([]);
        setSelectedOrganizations([]);
        setUseChat(true);
        setUseSms(false);
      },
      onError: error => {
        if (error?.response?.status === 400) {
          dispatch(
            setSnackbar({
              message: 'خطا در ایجاد اعلان مرفوک',
              severity: 'error',
            }),
          );
        } else dispatch(setSnackbar(error));

        setProgress(0);
        setLoading(false);
      },
    },
  );

  const errors = mutation.error?.response?.data || {};

  const submitForm = event => {
    abortController.current = new AbortController();
    event.preventDefault();
    mutation.mutate({ event });
  };

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        width: '100%',
        height: '100%',
        // overflowY: 'scroll',
        mt: 2,
      }}
    >
      <form
        onSubmit={submitForm}
        ref={formRef}
        style={{ width: '100%', display: 'flex', justifyContent: 'center' }}
      >
        <Grid item xs={12} lg={8} sx={{ mt: 2 }}>
          <Card>
            <CardContent>
              <Typography sx={{ fontSize: 16, fontWeight: 'bold', mb: 2 }}>
                دستور جزء به جزء
              </Typography>

              <TextField
                variant="outlined"
                required
                label="عنوان"
                name="title"
                defaultValue={content.title}
                helperText={errors.title}
                error={!!errors.title}
                disabled={mutation.isLoading}
                inputProps={{ maxLength: 100 }}
                fullWidth
                sx={{ mb: 2 }}
              />
              <TextField
                variant="outlined"
                required
                label="شماره دستور جز به جز"
                name="instructionNumber"
                type="number"
                value={instructionNumber}
                onChange={e => setInstructionNumber(e.target.value)}
                helperText={errors.instructionNumber}
                error={!!errors.instructionNumber}
                disabled={mutation.isLoading}
                fullWidth
              />

              <TextField
                sx={{ mt: 2 }}
                label="هشتگ‌های عمومی"
                placeholder="مثال: #تگ1, #تگ2, #تگ3 یا paste کنید"
                onBlur={e => {
                  handleGeneralHashtagInput(e.target.value);
                  e.target.value = '';
                }}
                onKeyDown={e => handleGeneralHashtagKeyDown(e)}
                onPaste={e => handleGeneralHashtagPaste(e)}
                disabled={mutation.isLoading}
                fullWidth
              />

              {generalHashtags.length > 0 && (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                  {generalHashtags.map((hashtag, index) => (
                    <Chip
                      key={`general-${index}`}
                      label={hashtag}
                      onDelete={() => removeGeneralHashtag(index)}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
              )}

              <TextField
                sx={{ mt: 2 }}
                label="توضیحات"
                name="body"
                variant="outlined"
                helperText={errors.body}
                multiline
                rows={4}
                fullWidth
                inputRef={descriptionRef}
                InputProps={{
                  endAdornment: (
                    <IconButton edge="end" onClick={pasteDescription}>
                      <ContentPasteGoOutlined />
                    </IconButton>
                  ),
                }}
              />
              <Stack flexDirection="row" gap={2}>
                <Box sx={{ mt: 2, width: '100%' }}>
                  <JalaliDateTimePicker
                    inputName="registration_date"
                    label="تاریخ ثبت"
                    size="medium"
                    defaultValue={registrationDate}
                    onChange={setRegistrationDate}
                    disabled={mutation.isLoading}
                    time
                  />
                </Box>

                <Box sx={{ mt: 2, width: '100%' }}>
                  <JalaliDateTimePicker
                    inputName="expiration_date"
                    label="تاریخ انقضا"
                    size="medium"
                    setMinDateToday
                    defaultValue={dayjs()}
                    onChange={setExpirationDate}
                    disabled={mutation.isLoading}
                    time
                  />
                </Box>
              </Stack>
            </CardContent>
          </Card>

          <Divider sx={{ mt: 3, mb: 3 }} />
          <Card>
            <CardContent>
              <Stack
                flexDirection="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography sx={{ fontSize: 16, fontWeight: 'bold', mb: 2 }}>
                  محور ها
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="contained"
                    startIcon={editingIndex >= 0 ? <Edit /> : <Add />}
                    onClick={addOrUpdateInstruction}
                    disabled={mutation.isLoading}
                  >
                    {editingIndex >= 0 ? 'ویرایش محور' : 'افزودن محور'}
                  </Button>
                  {editingIndex >= 0 && (
                    <Button
                      variant="outlined"
                      onClick={cancelEdit}
                      disabled={mutation.isLoading}
                    >
                      لغو
                    </Button>
                  )}
                </Box>
              </Stack>

              <TextField
                fullWidth
                label="عنوان محور"
                value={newInstructionTitle}
                onChange={e => setNewInstructionTitle(e.target.value)}
                sx={{ my: 2 }}
                placeholder="عنوان محور را وارد کنید"
              />

              <TextField
                fullWidth
                label="هشتگ‌ها"
                placeholder="مثال: تگ1, تگ2, تگ3 یا paste کنید"
                onBlur={e => {
                  handleNewInstructionHashtagInput(e.target.value);
                  e.target.value = '';
                }}
                onKeyDown={handleNewInstructionHashtagKeyDown}
                onPaste={e => handleNewInstructionHashtagPaste(e)}
                sx={{ mb: 2 }}
              />

              {newInstructionHashtags.length > 0 && (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {newInstructionHashtags.map((hashtag, index) => (
                    <Chip
                      key={`new-${index}`}
                      label={hashtag}
                      onDelete={() => removeNewInstructionHashtag(index)}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
              )}

              {/* Form for adding/editing instructions */}
              {instructions.length > 0 && (
                <Box
                  sx={{
                    p: 2,
                    border: '1px solid #e0e0e0',
                    borderRadius: 2,
                    // mb: 2,
                    backgroundColor: '#f9f9f9',
                  }}
                >
                  {/* Table of instructions */}

                  <TableContainer
                    component={Paper}
                    sx={{ background: 'transparent', boxShadow: 'none' }}
                  >
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>ردیف</TableCell>
                          <TableCell>عنوان محور</TableCell>
                          <TableCell>هشتگ‌ها</TableCell>
                          <TableCell>عملیات</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {instructions.map((instruction, index) => (
                          <TableRow key={index}>
                            <TableCell>{index + 1}</TableCell>
                            <TableCell>{instruction.title}</TableCell>
                            <TableCell>
                              <Box
                                sx={{
                                  display: 'flex',
                                  flexWrap: 'wrap',
                                  gap: 0.5,
                                }}
                              >
                                {instruction.hashtags.map(
                                  (hashtag, hashtagIndex) => (
                                    <Chip
                                      key={hashtagIndex}
                                      label={hashtag}
                                      size="small"
                                      color="primary"
                                      variant="outlined"
                                    />
                                  ),
                                )}
                              </Box>
                            </TableCell>
                            <TableCell>
                              <Box sx={{ display: 'flex', gap: 1 }}>
                                <IconButton
                                  size="small"
                                  color="primary"
                                  onClick={() => editInstruction(index)}
                                  disabled={mutation.isLoading}
                                >
                                  <Edit />
                                </IconButton>
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={() => removeInstruction(index)}
                                  disabled={mutation.isLoading}
                                >
                                  <Delete />
                                </IconButton>
                              </Box>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              )}
            </CardContent>
          </Card>

          <Button
            variant="contained"
            size="large"
            type="submit"
            sx={{ mt: 4, width: '100%' }}
            disabled={loading}
          >
            {!loading ? (
              <>ایجاد اعلان مرفوک</>
            ) : (
              <CircularProgress sx={{ color: 'white' }} size={24} />
            )}
          </Button>
        </Grid>
      </form>
    </Box>
  );
}
