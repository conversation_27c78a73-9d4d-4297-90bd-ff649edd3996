import {
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
} from '@mui/material';
import { isAudio, isVideo, isImage } from 'utils';
import { CONTENT_TYPES } from 'constants';
import { useEffect, useState } from 'react';

function getValidTypes(selectedFileType) {
  if (isVideo(selectedFileType)) {
    return CONTENT_TYPES.filter(type => type.fileTypes.includes('video'));
  }
  if (isImage(selectedFileType)) {
    return CONTENT_TYPES.filter(type => type.fileTypes.includes('image'));
  }
  if (isAudio(selectedFileType)) {
    return CONTENT_TYPES.filter(type => type.fileTypes.includes('audio'));
  }

  return CONTENT_TYPES;
}

export default function Type({ type, errors, disabled, selectedFileType }) {
  const validTypes = selectedFileType
    ? getValidTypes(selectedFileType)
    : CONTENT_TYPES;

  const [inputVal, setInputVal] = useState(type || validTypes[0].value);

  useEffect(() => {
    setInputVal(type || validTypes[0].value);
  }, [type, selectedFileType]);

  return (
    <FormControl fullWidth>
      <InputLabel>نوع محتوا</InputLabel>
      <Select
        label="نوع محتوا"
        name="type"
        value={inputVal}
        disabled={disabled}
        required
        onChange={e => setInputVal(e.target.value)}
      >
        {validTypes.map(_type => (
          <MenuItem value={_type.value} key={_type.value}>
            {_type.name}
          </MenuItem>
        ))}
      </Select>
      <FormHelperText>{errors}</FormHelperText>
    </FormControl>
  );
}
